"""
RedisCacheService - Redis缓存服务封装

提供高级缓存操作，包括序列化、版本控制、批量操作等功能
"""

import json
import pickle
import asyncio
from typing import Any, Dict, List, Optional, Union, Callable, TypeVar, Generic
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

from commonlib.core.logging.tsif_logging import app_logger
from commonlib.storages.persistence.redis.repository import RedisRepository

T = TypeVar('T')


class SerializationType(Enum):
    """序列化类型"""
    JSON = "json"
    PICKLE = "pickle"
    STRING = "string"


@dataclass
class CacheConfig:
    """缓存配置"""
    default_ttl: int = 3600  # 默认过期时间（秒）
    max_retries: int = 3     # 最大重试次数
    retry_delay: float = 0.1 # 重试延迟（秒）
    enable_compression: bool = False  # 是否启用压缩
    version_enabled: bool = True      # 是否启用版本控制


@dataclass
class CacheItem:
    """缓存项"""
    value: Any
    created_at: datetime
    ttl: Optional[int] = None
    version: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class RedisCacheService(Generic[T]):
    """Redis缓存服务"""
    
    def __init__(
        self,
        redis_repo: RedisRepository,
        config: Optional[CacheConfig] = None
    ):
        """
        初始化缓存服务
        
        Args:
            redis_repo: Redis仓库实例
            config: 缓存配置
        """
        self.redis_repo = redis_repo
        self.config = config or CacheConfig()
    
    # ==================== 基础缓存操作 ====================
    
    async def set(
        self,
        key: str,
        value: T,
        ttl: Optional[int] = None,
        serialization: SerializationType = SerializationType.JSON,
        version: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        设置缓存值
        
        Args:
            key: 缓存键
            value: 缓存值
            ttl: 过期时间（秒）
            serialization: 序列化类型
            version: 版本号
            metadata: 元数据
            
        Returns:
            是否设置成功
        """
        try:
            # 构建缓存项
            cache_item = CacheItem(
                value=value,
                created_at=datetime.now(),
                ttl=ttl,
                version=version,
                metadata=metadata
            )
            
            # 序列化缓存项
            serialized_value = self._serialize(cache_item, serialization)
            
            # 设置到Redis
            actual_ttl = ttl or self.config.default_ttl
            return await self.redis_repo.set(key, serialized_value, ttl=actual_ttl)
            
        except Exception as e:
            # 记录错误但不抛出异常
            app_logger.error(f"Cache set error for key {key}: {e}")
            return False
    
    async def get(
        self,
        key: str,
        serialization: SerializationType = SerializationType.JSON,
        default: Optional[T] = None
    ) -> Optional[T]:
        """
        获取缓存值
        
        Args:
            key: 缓存键
            serialization: 序列化类型
            default: 默认值
            
        Returns:
            缓存值或默认值
        """
        try:
            # 从Redis获取
            serialized_value = await self.redis_repo.get(key)
            if serialized_value is None:
                return default
            
            # 反序列化
            cache_item = self._deserialize(serialized_value, serialization)
            if not isinstance(cache_item, CacheItem):
                return default
                
            return cache_item.value
            
        except Exception as e:
            app_logger.error(f"Cache get error for key {key}: {e}", exception=True)
            await self.delete(key)
            return default
    
    async def get_with_metadata(
        self,
        key: str,
        serialization: SerializationType = SerializationType.JSON
    ) -> Optional[CacheItem]:
        """
        获取缓存值及元数据
        
        Args:
            key: 缓存键
            serialization: 序列化类型
            
        Returns:
            缓存项或None
        """
        try:
            serialized_value = await self.redis_repo.get(key)
            if serialized_value is None:
                return None
                
            return self._deserialize(serialized_value, serialization)
            
        except Exception as e:
            app_logger.error(f"Cache get_with_metadata error for key {key}: {e}")
            await self.delete(key)
            return None
    
    async def delete(self, key: str) -> bool:
        """删除缓存"""
        try:
            return await self.redis_repo.delete(key)
        except Exception as e:
            app_logger.error(f"Cache delete error for key {key}: {e}")
            return False
    
    async def exists(self, key: str) -> bool:
        """检查缓存是否存在"""
        try:
            return await self.redis_repo.exists(key)
        except Exception as e:
            app_logger.error(f"Cache exists error for key {key}: {e}")
            return False
    
    async def expire(self, key: str, seconds: int) -> bool:
        """设置缓存过期时间"""
        try:
            return await self.redis_repo.expire(key, seconds)
        except Exception as e:
            app_logger.error(f"Cache expire error for key {key}: {e}")
            return False
    
    async def ttl(self, key: str) -> int:
        """获取缓存剩余生存时间"""
        try:
            return await self.redis_repo.ttl(key)
        except Exception as e:
            app_logger.error(f"Cache ttl error for key {key}: {e}")
            return -1
    
    # ==================== 批量操作 ====================
    
    async def mset(
        self,
        mapping: Dict[str, T],
        ttl: Optional[int] = None,
        serialization: SerializationType = SerializationType.JSON
    ) -> bool:
        """
        批量设置缓存
        
        Args:
            mapping: 键值对映射
            ttl: 过期时间
            serialization: 序列化类型
            
        Returns:
            是否设置成功
        """
        try:
            # 序列化所有值
            serialized_mapping = {}
            for key, value in mapping.items():
                cache_item = CacheItem(
                    value=value,
                    created_at=datetime.now(),
                    ttl=ttl
                )
                serialized_mapping[key] = self._serialize(cache_item, serialization)
            
            # 批量设置
            success = await self.redis_repo.mset(serialized_mapping)
            
            # 如果有TTL，需要单独设置过期时间
            if success and ttl:
                tasks = [
                    self.redis_repo.expire(key, ttl)
                    for key in mapping.keys()
                ]
                await asyncio.gather(*tasks, return_exceptions=True)
            
            return success
            
        except Exception as e:
            app_logger.error(f"Cache mset error: {e}")
            return False
    
    async def mget(
        self,
        keys: List[str],
        serialization: SerializationType = SerializationType.JSON
    ) -> Dict[str, Optional[T]]:
        """
        批量获取缓存
        
        Args:
            keys: 缓存键列表
            serialization: 序列化类型
            
        Returns:
            键值对映射
        """
        try:
            # 批量获取
            values = await self.redis_repo.mget(keys)
            
            # 反序列化结果
            result = {}
            for key, serialized_value in zip(keys, values):
                if serialized_value is not None:
                    try:
                        cache_item = self._deserialize(serialized_value, serialization)
                        result[key] = cache_item.value if isinstance(cache_item, CacheItem) else None
                    except Exception:
                        result[key] = None
                else:
                    result[key] = None
            
            return result
            
        except Exception as e:
            app_logger.error(f"Cache mget error: {e}")
            return {key: None for key in keys}
    


    # ==================== 序列化方法 ====================
    
    def _serialize(self, value: Any, serialization: SerializationType) -> str:
        """序列化值"""
        if serialization == SerializationType.JSON:
            return json.dumps(value, default=self._json_serializer, ensure_ascii=False)
        elif serialization == SerializationType.PICKLE:
            return pickle.dumps(value).hex()
        elif serialization == SerializationType.STRING:
            return str(value)
        else:
            raise ValueError(f"Unsupported serialization type: {serialization}")
    
    def _deserialize(self, value: str, serialization: SerializationType) -> Any:
        """反序列化值"""
        if serialization == SerializationType.JSON:
            return json.loads(value, object_hook=self._json_deserializer)
        elif serialization == SerializationType.PICKLE:
            return pickle.loads(bytes.fromhex(value))
        elif serialization == SerializationType.STRING:
            return value
        else:
            raise ValueError(f"Unsupported serialization type: {serialization}")


    def _json_serializer(self, obj: Any) -> Any:
        """JSON序列化器"""
        if isinstance(obj, datetime):
            return {"__datetime__": obj.isoformat()}
        elif isinstance(obj, CacheItem):
            return {
                "__cache_item__": {
                    "value": obj.value,
                    "created_at": obj.created_at.isoformat(),
                    "ttl": obj.ttl,
                    "version": obj.version,
                    "metadata": obj.metadata
                }
            }
        return obj
    
    def _json_deserializer(self, obj: Dict[str, Any]) -> Any:
        """JSON反序列化器"""
        if "__datetime__" in obj:
            return datetime.fromisoformat(obj["__datetime__"])
        elif "__cache_item__" in obj:
            data = obj["__cache_item__"]
            return CacheItem(
                value=data["value"],
                created_at=datetime.fromisoformat(data["created_at"]),
                ttl=data["ttl"],
                version=data["version"],
                metadata=data["metadata"]
            )
        return obj

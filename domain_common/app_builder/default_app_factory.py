from contextlib import asynccontextmanager
from typing import AsyncIterator, List

from container import ServiceContainer
from commonlib.configs.base_setting import AppSettings
from commonlib.core.containers.config_container import ConfigContainer
from commonlib.core.containers.infra_container import InfraContainer
from commonlib.core.logging.tsif_logging import app_logger
from domain_common.app_builder.default_app_builder import App<PERSON>uilder
from fastapi import FastAPI


class AppInitializer:
    def __init__(
        self,
        config: ConfigContainer,
        infra: InfraContainer,
        services: ServiceContainer,
        wire_modules: List[str],
    ):
        self._config: ConfigContainer = config
        self._infra = infra
        self._services = services
        self._wire_modules = wire_modules

    @asynccontextmanager
    async def lifespan(self, app: FastAPI) -> AsyncIterator[None]:
        """增强的生命周期管理（带健康检查）"""

        try:
            await self._infra.init_resources()
            self._infra.wire(
                modules=[
                    "domain_common.interface.infra_redis.rd_decorator",  # 装饰器定义模块
                    "domain_common.interface.infra_snowflake",  # 装饰器定义模块
                    "domain_common.common_health",  # 使用装饰器的模块
                    *self._wire_modules,
                ]
            )

            await self._services.init_resources()
            self._services.wire(modules=[*self._wire_modules])  # 显式声明注入范围
            app.state.containers = {
                "configs": self._config,
                "infra": self._infra,
                "services": self._services,
            }
            boot_service = await self._services.system_boot_service()
            await boot_service.initialize_system()
            # await self._services.system_boot_service().initialize_system()
            AppBuilder.start_app_scheduler()
            yield

        except Exception as e:
            app_logger.exception(f"Startup failed: {str(e)}", exception=True)
            raise
        finally:
            await self._infra.shutdown_resources()

    def create_app(self) -> FastAPI:
        """优化后的应用工厂"""
        config: AppSettings = self._config.config()
        app_config = config.application
        app = FastAPI(
            debug=app_config.debug,
            title=app_config.title,
            description=app_config.description,
            docs_url=app_config.docs_url,
            openapi_url=app_config.openapi_url,
            redoc_url=app_config.redoc_url,
            lifespan=self.lifespan,
        )
        # 配置应用
        AppBuilder.register_exception_handlers(app, config)
        AppBuilder.setup_middlewares(app, config)
        AppBuilder.register_health_router(app)
        return app

"""
安全配置

定义JWT、会话、缓存等安全相关的配置参数
"""

import os
from typing import Dict, Any, Optional
from dataclasses import dataclass


@dataclass
class JWTConfig:
    """JWT配置"""
    # 算法配置
    algorithm: str = "RS256"
    
    # 令牌有效期
    access_token_expire_minutes: int = 30
    refresh_token_expire_days: int = 7
    
    # 密钥配置
    private_key_path: Optional[str] = None
    public_key_path: Optional[str] = None
    private_key: Optional[str] = None
    public_key: Optional[str] = None
    
    # 令牌配置
    issuer: str = "iam-service"
    audience: str = "rag-platform"
    
    # 安全配置
    enable_token_rotation: bool = True
    enable_device_binding: bool = True
    max_refresh_attempts: int = 3


@dataclass
class SessionConfig:
    """会话配置"""
    # 会话超时
    session_timeout_minutes: int = 120
    remember_me_days: int = 30
    
    # 并发控制
    max_concurrent_sessions: int = 5
    single_device_login: bool = False
    
    # 设备跟踪
    enable_device_tracking: bool = True
    enable_ip_tracking: bool = True
    enable_location_tracking: bool = False
    
    # 安全检查
    check_device_fingerprint: bool = True
    check_ip_change: bool = True
    suspicious_ip_distance_km: float = 100.0
    suspicious_time_window_minutes: int = 60


@dataclass
class CacheConfig:
    """缓存配置"""
    # 基础缓存TTL（秒）
    user_info_ttl: int = 3600  # 1小时
    user_roles_ttl: int = 1800  # 30分钟
    user_permissions_ttl: int = 1800  # 30分钟
    role_permissions_ttl: int = 3600  # 1小时
    session_ttl: int = 7200  # 2小时
    permission_check_ttl: int = 300  # 5分钟
    
    # 缓存策略
    enable_cache_compression: bool = True
    enable_cache_encryption: bool = False
    cache_key_prefix: str = "iam"
    
    # 性能配置
    max_cache_size_mb: int = 512
    cache_cleanup_interval_minutes: int = 60


@dataclass
class SecurityConfig:
    """安全配置"""
    # 密码策略
    password_min_length: int = 8
    password_max_length: int = 128
    password_require_uppercase: bool = True
    password_require_lowercase: bool = True
    password_require_digits: bool = True
    password_require_special_chars: bool = True
    password_max_repeated_chars: int = 3
    password_prevent_common: bool = True
    password_history_count: int = 5
    
    # 登录安全
    max_login_attempts: int = 5
    login_lockout_minutes: int = 30
    enable_captcha_after_attempts: int = 3
    
    # 限流配置
    rate_limit_login_per_minute: int = 10
    rate_limit_api_per_minute: int = 100
    rate_limit_password_reset_per_hour: int = 3
    
    # 审计配置
    enable_audit_logging: bool = True
    audit_sensitive_operations: bool = True
    audit_retention_days: int = 90
    
    # 加密配置
    encryption_key: Optional[str] = None
    hash_rounds: int = 12


@dataclass
class MonitoringConfig:
    """监控配置"""
    # 性能监控
    enable_performance_monitoring: bool = True
    slow_query_threshold_ms: int = 1000
    
    # 安全监控
    enable_security_monitoring: bool = True
    alert_failed_login_threshold: int = 10
    alert_suspicious_activity: bool = True
    
    # 健康检查
    health_check_interval_seconds: int = 30
    cache_health_check: bool = True
    database_health_check: bool = True


class SecurityConfigManager:
    """安全配置管理器"""
    
    def __init__(self):
        self.jwt_config = self._load_jwt_config()
        self.session_config = self._load_session_config()
        self.cache_config = self._load_cache_config()
        self.security_config = self._load_security_config()
        self.monitoring_config = self._load_monitoring_config()
    
    def _load_jwt_config(self) -> JWTConfig:
        """加载JWT配置"""
        return JWTConfig(
            algorithm=os.getenv("JWT_ALGORITHM", "RS256"),
            access_token_expire_minutes=int(os.getenv("JWT_ACCESS_TOKEN_EXPIRE_MINUTES", "30")),
            refresh_token_expire_days=int(os.getenv("JWT_REFRESH_TOKEN_EXPIRE_DAYS", "7")),
            private_key_path=os.getenv("JWT_PRIVATE_KEY_PATH"),
            public_key_path=os.getenv("JWT_PUBLIC_KEY_PATH"),
            private_key=os.getenv("JWT_PRIVATE_KEY"),
            public_key=os.getenv("JWT_PUBLIC_KEY"),
            issuer=os.getenv("JWT_ISSUER", "iam-service"),
            audience=os.getenv("JWT_AUDIENCE", "rag-platform"),
            enable_token_rotation=os.getenv("JWT_ENABLE_TOKEN_ROTATION", "true").lower() == "true",
            enable_device_binding=os.getenv("JWT_ENABLE_DEVICE_BINDING", "true").lower() == "true",
            max_refresh_attempts=int(os.getenv("JWT_MAX_REFRESH_ATTEMPTS", "3"))
        )
    
    def _load_session_config(self) -> SessionConfig:
        """加载会话配置"""
        return SessionConfig(
            session_timeout_minutes=int(os.getenv("SESSION_TIMEOUT_MINUTES", "120")),
            remember_me_days=int(os.getenv("SESSION_REMEMBER_ME_DAYS", "30")),
            max_concurrent_sessions=int(os.getenv("SESSION_MAX_CONCURRENT", "5")),
            single_device_login=os.getenv("SESSION_SINGLE_DEVICE", "false").lower() == "true",
            enable_device_tracking=os.getenv("SESSION_ENABLE_DEVICE_TRACKING", "true").lower() == "true",
            enable_ip_tracking=os.getenv("SESSION_ENABLE_IP_TRACKING", "true").lower() == "true",
            enable_location_tracking=os.getenv("SESSION_ENABLE_LOCATION_TRACKING", "false").lower() == "true",
            check_device_fingerprint=os.getenv("SESSION_CHECK_DEVICE_FINGERPRINT", "true").lower() == "true",
            check_ip_change=os.getenv("SESSION_CHECK_IP_CHANGE", "true").lower() == "true",
            suspicious_ip_distance_km=float(os.getenv("SESSION_SUSPICIOUS_IP_DISTANCE_KM", "100.0")),
            suspicious_time_window_minutes=int(os.getenv("SESSION_SUSPICIOUS_TIME_WINDOW_MINUTES", "60"))
        )
    
    def _load_cache_config(self) -> CacheConfig:
        """加载缓存配置"""
        return CacheConfig(
            user_info_ttl=int(os.getenv("CACHE_USER_INFO_TTL", "3600")),
            user_roles_ttl=int(os.getenv("CACHE_USER_ROLES_TTL", "1800")),
            user_permissions_ttl=int(os.getenv("CACHE_USER_PERMISSIONS_TTL", "1800")),
            role_permissions_ttl=int(os.getenv("CACHE_ROLE_PERMISSIONS_TTL", "3600")),
            session_ttl=int(os.getenv("CACHE_SESSION_TTL", "7200")),
            permission_check_ttl=int(os.getenv("CACHE_PERMISSION_CHECK_TTL", "300")),
            enable_cache_compression=os.getenv("CACHE_ENABLE_COMPRESSION", "true").lower() == "true",
            enable_cache_encryption=os.getenv("CACHE_ENABLE_ENCRYPTION", "false").lower() == "true",
            cache_key_prefix=os.getenv("CACHE_KEY_PREFIX", "iam"),
            max_cache_size_mb=int(os.getenv("CACHE_MAX_SIZE_MB", "512")),
            cache_cleanup_interval_minutes=int(os.getenv("CACHE_CLEANUP_INTERVAL_MINUTES", "60"))
        )
    
    def _load_security_config(self) -> SecurityConfig:
        """加载安全配置"""
        return SecurityConfig(
            password_min_length=int(os.getenv("SECURITY_PASSWORD_MIN_LENGTH", "8")),
            password_max_length=int(os.getenv("SECURITY_PASSWORD_MAX_LENGTH", "128")),
            password_require_uppercase=os.getenv("SECURITY_PASSWORD_REQUIRE_UPPERCASE", "true").lower() == "true",
            password_require_lowercase=os.getenv("SECURITY_PASSWORD_REQUIRE_LOWERCASE", "true").lower() == "true",
            password_require_digits=os.getenv("SECURITY_PASSWORD_REQUIRE_DIGITS", "true").lower() == "true",
            password_require_special_chars=os.getenv("SECURITY_PASSWORD_REQUIRE_SPECIAL_CHARS", "true").lower() == "true",
            password_max_repeated_chars=int(os.getenv("SECURITY_PASSWORD_MAX_REPEATED_CHARS", "3")),
            password_prevent_common=os.getenv("SECURITY_PASSWORD_PREVENT_COMMON", "true").lower() == "true",
            password_history_count=int(os.getenv("SECURITY_PASSWORD_HISTORY_COUNT", "5")),
            max_login_attempts=int(os.getenv("SECURITY_MAX_LOGIN_ATTEMPTS", "5")),
            login_lockout_minutes=int(os.getenv("SECURITY_LOGIN_LOCKOUT_MINUTES", "30")),
            enable_captcha_after_attempts=int(os.getenv("SECURITY_ENABLE_CAPTCHA_AFTER_ATTEMPTS", "3")),
            rate_limit_login_per_minute=int(os.getenv("SECURITY_RATE_LIMIT_LOGIN_PER_MINUTE", "10")),
            rate_limit_api_per_minute=int(os.getenv("SECURITY_RATE_LIMIT_API_PER_MINUTE", "100")),
            rate_limit_password_reset_per_hour=int(os.getenv("SECURITY_RATE_LIMIT_PASSWORD_RESET_PER_HOUR", "3")),
            enable_audit_logging=os.getenv("SECURITY_ENABLE_AUDIT_LOGGING", "true").lower() == "true",
            audit_sensitive_operations=os.getenv("SECURITY_AUDIT_SENSITIVE_OPERATIONS", "true").lower() == "true",
            audit_retention_days=int(os.getenv("SECURITY_AUDIT_RETENTION_DAYS", "90")),
            encryption_key=os.getenv("SECURITY_ENCRYPTION_KEY"),
            hash_rounds=int(os.getenv("SECURITY_HASH_ROUNDS", "12"))
        )
    
    def _load_monitoring_config(self) -> MonitoringConfig:
        """加载监控配置"""
        return MonitoringConfig(
            enable_performance_monitoring=os.getenv("MONITORING_ENABLE_PERFORMANCE", "true").lower() == "true",
            slow_query_threshold_ms=int(os.getenv("MONITORING_SLOW_QUERY_THRESHOLD_MS", "1000")),
            enable_security_monitoring=os.getenv("MONITORING_ENABLE_SECURITY", "true").lower() == "true",
            alert_failed_login_threshold=int(os.getenv("MONITORING_ALERT_FAILED_LOGIN_THRESHOLD", "10")),
            alert_suspicious_activity=os.getenv("MONITORING_ALERT_SUSPICIOUS_ACTIVITY", "true").lower() == "true",
            health_check_interval_seconds=int(os.getenv("MONITORING_HEALTH_CHECK_INTERVAL_SECONDS", "30")),
            cache_health_check=os.getenv("MONITORING_CACHE_HEALTH_CHECK", "true").lower() == "true",
            database_health_check=os.getenv("MONITORING_DATABASE_HEALTH_CHECK", "true").lower() == "true"
        )
    
    def get_all_configs(self) -> Dict[str, Any]:
        """获取所有配置"""
        return {
            "jwt": self.jwt_config,
            "session": self.session_config,
            "cache": self.cache_config,
            "security": self.security_config,
            "monitoring": self.monitoring_config
        }
    
    def validate_configs(self) -> Dict[str, Any]:
        """验证配置有效性"""
        # TODO: 实现配置验证逻辑
        # 1. 检查必需的配置项
        # 2. 验证配置值的合理性
        # 3. 检查配置之间的依赖关系
        # 4. 返回验证结果
        
        validation_result = {
            "is_valid": True,
            "errors": [],
            "warnings": []
        }
        
        # 验证JWT配置
        if not self.jwt_config.private_key and not self.jwt_config.private_key_path:
            validation_result["errors"].append("JWT私钥未配置")
            validation_result["is_valid"] = False
        
        # 验证会话配置
        if self.session_config.session_timeout_minutes < 5:
            validation_result["warnings"].append("会话超时时间过短，建议至少5分钟")
        
        # 验证安全配置
        if self.security_config.password_min_length < 6:
            validation_result["warnings"].append("密码最小长度建议至少6位")
        
        return validation_result


# 全局配置实例
security_config_manager = SecurityConfigManager()

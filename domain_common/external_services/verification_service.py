"""
验证码服务

统一管理短信验证码、邮箱验证码等各种验证码的生成、发送和验证
"""

import uuid
import secrets
import string
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
from enum import Enum

from commonlib.exceptions.exceptions import ValidationError, BusinessError
from .email_service import EmailService
from .sms_service import SMSService


class VerificationCodeType(Enum):
    """验证码类型"""
    SMS = "sms"
    EMAIL = "email"
    TOTP = "totp"


class VerificationCodeScene(Enum):
    """验证码使用场景"""
    USER_REGISTRATION = "user_registration"
    USER_ACTIVATION = "user_activation"
    PASSWORD_RESET = "password_reset"
    CHANGE_PHONE = "change_phone"
    CHANGE_EMAIL = "change_email"
    LOGIN_VERIFICATION = "login_verification"
    SENSITIVE_OPERATION = "sensitive_operation"


class VerificationService:
    """验证码服务类"""
    
    def __init__(
        self,
        redis_repo,
        email_service: Optional[EmailService] = None,
        sms_service: Optional[SMSService] = None,
        default_code_length: int = 6,
        default_expire_minutes: int = 5,
        max_attempts: int = 3,
        rate_limit_minutes: int = 1
    ):
        self.redis_repo = redis_repo
        self.email_service = email_service
        self.sms_service = sms_service
        self.default_code_length = default_code_length
        self.default_expire_minutes = default_expire_minutes
        self.max_attempts = max_attempts
        self.rate_limit_minutes = rate_limit_minutes

    async def send_sms_code(
        self,
        phone: str,
        scene: VerificationCodeScene,
        tenant_name: str,
        user_id: Optional[str] = None,
        code_length: Optional[int] = None,
        expire_minutes: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        发送短信验证码
        
        返回验证码ID和发送结果
        """
        if not self.sms_service:
            raise BusinessError("短信服务未配置")
        
        # 检查发送频率限制
        if not await self._check_send_rate_limit(phone, VerificationCodeType.SMS):
            raise BusinessError("验证码发送频率过高，请稍后再试")
        
        # 生成验证码
        code_length = code_length or self.default_code_length
        expire_minutes = expire_minutes or self.default_expire_minutes
        verification_code = self._generate_numeric_code(code_length)
        
        # 生成验证码ID
        code_id = f"sms_code_{uuid.uuid4()}"
        
        # 存储验证码信息
        await self._store_verification_code(
            code_id=code_id,
            code=verification_code,
            code_type=VerificationCodeType.SMS,
            target=phone,
            scene=scene,
            user_id=user_id,
            expire_minutes=expire_minutes
        )
        
        # 发送短信
        try:
            sms_result = await self.sms_service.send_verification_code(
                phone=phone,
                verification_code=verification_code,
                tenant_name=tenant_name
            )
            
            # 记录发送成功
            await self._record_send_success(code_id, sms_result)
            
            return {
                "code_id": code_id,
                "target": self._mask_phone(phone),
                "expire_seconds": expire_minutes * 60,
                "sent_at": datetime.utcnow().isoformat(),
                "scene": scene.value
            }
            
        except Exception as e:
            # 删除已存储的验证码
            await self._delete_verification_code(code_id)
            raise BusinessError(f"短信发送失败: {str(e)}")

    async def send_email_code(
        self,
        email: str,
        scene: VerificationCodeScene,
        tenant_name: str,
        username: str,
        user_id: Optional[str] = None,
        code_length: Optional[int] = None,
        expire_minutes: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        发送邮箱验证码
        
        返回验证码ID和发送结果
        """
        if not self.email_service:
            raise BusinessError("邮件服务未配置")
        
        # 检查发送频率限制
        if not await self._check_send_rate_limit(email, VerificationCodeType.EMAIL):
            raise BusinessError("验证码发送频率过高，请稍后再试")
        
        # 生成验证码
        code_length = code_length or self.default_code_length
        expire_minutes = expire_minutes or self.default_expire_minutes
        verification_code = self._generate_numeric_code(code_length)
        
        # 生成验证码ID
        code_id = f"email_code_{uuid.uuid4()}"
        
        # 存储验证码信息
        await self._store_verification_code(
            code_id=code_id,
            code=verification_code,
            code_type=VerificationCodeType.EMAIL,
            target=email,
            scene=scene,
            user_id=user_id,
            expire_minutes=expire_minutes
        )
        
        # 发送邮件
        try:
            email_result = await self.email_service.send_verification_code_email(
                to_email=email,
                username=username,
                tenant_name=tenant_name,
                verification_code=verification_code
            )
            
            if not email_result:
                raise BusinessError("邮件发送失败")
            
            # 记录发送成功
            await self._record_send_success(code_id, {"success": True})
            
            return {
                "code_id": code_id,
                "target": self._mask_email(email),
                "expire_seconds": expire_minutes * 60,
                "sent_at": datetime.utcnow().isoformat(),
                "scene": scene.value
            }
            
        except Exception as e:
            # 删除已存储的验证码
            await self._delete_verification_code(code_id)
            raise BusinessError(f"邮件发送失败: {str(e)}")

    async def verify_code(
        self,
        code_id: str,
        verification_code: str,
        scene: Optional[VerificationCodeScene] = None
    ) -> Dict[str, Any]:
        """
        验证验证码
        
        返回验证结果和相关信息
        """
        # 获取验证码信息
        code_info = await self._get_verification_code(code_id)
        if not code_info:
            raise ValidationError("验证码不存在或已过期")
        
        # 检查场景匹配
        if scene and code_info["scene"] != scene.value:
            raise ValidationError("验证码使用场景不匹配")
        
        # 检查验证次数
        if code_info.get("attempts", 0) >= self.max_attempts:
            await self._delete_verification_code(code_id)
            raise ValidationError("验证码验证失败次数过多")
        
        # 验证验证码
        if code_info["code"] != verification_code:
            # 增加验证失败次数
            await self._increment_verification_attempts(code_id)
            raise ValidationError("验证码错误")
        
        # 验证成功，删除验证码
        await self._delete_verification_code(code_id)
        
        # 记录验证成功
        await self._record_verification_success(code_id, code_info)
        
        return {
            "code_id": code_id,
            "verified": True,
            "verified_at": datetime.utcnow().isoformat(),
            "target": code_info["target"],
            "scene": code_info["scene"],
            "user_id": code_info.get("user_id")
        }

    async def get_code_info(self, code_id: str) -> Optional[Dict[str, Any]]:
        """获取验证码信息（不包含验证码本身）"""
        code_info = await self._get_verification_code(code_id)
        if not code_info:
            return None
        
        # 移除敏感信息
        safe_info = code_info.copy()
        safe_info.pop("code", None)
        
        return safe_info

    # ===== 私有方法 =====

    def _generate_numeric_code(self, length: int) -> str:
        """生成数字验证码"""
        return ''.join(secrets.choice(string.digits) for _ in range(length))

    def _generate_alphanumeric_code(self, length: int) -> str:
        """生成字母数字验证码"""
        characters = string.ascii_uppercase + string.digits
        return ''.join(secrets.choice(characters) for _ in range(length))

    async def _store_verification_code(
        self,
        code_id: str,
        code: str,
        code_type: VerificationCodeType,
        target: str,
        scene: VerificationCodeScene,
        user_id: Optional[str],
        expire_minutes: int
    ):
        """存储验证码信息"""
        code_info = {
            "code_id": code_id,
            "code": code,
            "type": code_type.value,
            "target": target,
            "scene": scene.value,
            "user_id": user_id,
            "created_at": datetime.utcnow().isoformat(),
            "expires_at": (datetime.utcnow() + timedelta(minutes=expire_minutes)).isoformat(),
            "attempts": 0,
            "verified": False
        }
        
        await self.redis_repo.set(
            f"verification_code:{code_id}",
            code_info,
            ttl=expire_minutes * 60
        )

    async def _get_verification_code(self, code_id: str) -> Optional[Dict[str, Any]]:
        """获取验证码信息"""
        return await self.redis_repo.get(f"verification_code:{code_id}")

    async def _delete_verification_code(self, code_id: str):
        """删除验证码"""
        await self.redis_repo.delete(f"verification_code:{code_id}")

    async def _increment_verification_attempts(self, code_id: str):
        """增加验证失败次数"""
        code_info = await self._get_verification_code(code_id)
        if code_info:
            code_info["attempts"] = code_info.get("attempts", 0) + 1
            
            # 重新计算TTL
            expires_at = datetime.fromisoformat(code_info["expires_at"])
            remaining_seconds = int((expires_at - datetime.utcnow()).total_seconds())
            
            if remaining_seconds > 0:
                await self.redis_repo.set(
                    f"verification_code:{code_id}",
                    code_info,
                    ttl=remaining_seconds
                )

    async def _check_send_rate_limit(self, target: str, code_type: VerificationCodeType) -> bool:
        """检查发送频率限制"""
        rate_limit_key = f"verification_rate_limit:{code_type.value}:{target}"
        last_send_time = await self.redis_repo.get(rate_limit_key)
        
        if last_send_time:
            last_time = datetime.fromisoformat(last_send_time)
            if datetime.utcnow() - last_time < timedelta(minutes=self.rate_limit_minutes):
                return False
        
        # 记录本次发送时间
        await self.redis_repo.set(
            rate_limit_key,
            datetime.utcnow().isoformat(),
            ttl=self.rate_limit_minutes * 60
        )
        
        return True

    async def _record_send_success(self, code_id: str, send_result: Dict[str, Any]):
        """记录发送成功"""
        # 可以记录到数据库或日志系统
        print(f"验证码发送成功: {code_id} - {send_result}")

    async def _record_verification_success(self, code_id: str, code_info: Dict[str, Any]):
        """记录验证成功"""
        # 可以记录到数据库或日志系统
        print(f"验证码验证成功: {code_id} - {code_info['target']}")

    def _mask_phone(self, phone: str) -> str:
        """手机号脱敏"""
        if len(phone) <= 7:
            return '*' * len(phone)
        return f"{phone[:3]}****{phone[-4:]}"

    def _mask_email(self, email: str) -> str:
        """邮箱脱敏"""
        if '@' not in email:
            return email
        
        local, domain = email.split('@', 1)
        if len(local) <= 2:
            masked_local = '*' * len(local)
        else:
            masked_local = local[:2] + '*' * (len(local) - 2)
        
        return f"{masked_local}@{domain}"

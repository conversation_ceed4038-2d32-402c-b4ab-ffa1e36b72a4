"""
租户初始化引导程序

负责为新创建的租户初始化默认角色、权限和管理员用户
"""

import uuid
import hashlib
import secrets
from datetime import datetime
from typing import Dict, Any, Optional, List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_

from domain_common.models import CommonStatus
from domain_common.models.constants import RoleType, UserGroupType, AssignmentType
from domain_common.models.iam_models import (
    Tenant, User, Role, Permission, UserRole, RolePermission,
    UserGroup, UserGroupMember, UserGroupRole
)


class TenantBootstrap:
    """租户初始化引导类
    
    为新租户创建：
    1. 租户级默认角色
    2. 租户级默认权限
    3. 租户管理员用户
    4. 租户默认用户组
    """
    
    def __init__(self, session: AsyncSession):
        self.session = session
    
    async def initialize_tenant(
        self,
        tenant: Tenant,
        admin_username: str,
        admin_email: str,
        admin_password: Optional[str] = None,
        create_default_groups: bool = True
    ) -> Dict[str, Any]:
        """
        租户完整初始化
        
        Args:
            tenant: 租户实例
            admin_username: 租户管理员用户名
            admin_email: 租户管理员邮箱
            admin_password: 租户管理员密码（如果为空则自动生成）
            create_default_groups: 是否创建默认用户组
            
        Returns:
            初始化结果信息
        """
        
        result = {
            "tenant_id": tenant.tenant_id,
            "tenant_code": tenant.tenant_code,
            "initialized_at": datetime.utcnow().isoformat(),
            "admin_user": {},
            "roles": [],
            "permissions": [],
            "groups": [],
            "generated_password": None
        }
        
        try:
            # 1. 创建租户级权限
            permissions = await self._create_tenant_permissions(tenant.tenant_id)
            result["permissions"] = [p.permission_code for p in permissions]
            
            # 2. 创建租户级角色
            roles = await self._create_tenant_roles(tenant.tenant_id)
            result["roles"] = [r.role_code for r in roles]
            
            # 3. 为角色分配权限
            await self._assign_permissions_to_roles(tenant.tenant_id, roles, permissions)
            
            # 4. 创建默认用户组（可选）
            groups = []
            if create_default_groups:
                groups = await self._create_default_groups(tenant.tenant_id)
                result["groups"] = [g.group_code for g in groups]
            
            # 5. 创建租户管理员用户
            if not admin_password:
                admin_password = self._generate_secure_password()
                result["generated_password"] = admin_password
            
            admin_user = await self._create_tenant_admin_user(
                tenant.tenant_id, admin_username, admin_email, admin_password
            )
            result["admin_user"] = {
                "user_id": admin_user.user_id,
                "username": admin_user.username,
                "email": admin_user.email
            }
            
            # 6. 为管理员分配租户超级管理员角色
            tenant_super_admin_role = next(
                r for r in roles if r.role_type == RoleType.TENANT_SUPER_ADMIN
            )
            await self._assign_role_to_user(
                tenant.tenant_id, admin_user, tenant_super_admin_role
            )
            
            # 7. 将管理员加入管理员组（如果创建了用户组）
            if groups:
                admin_group = next(
                    (g for g in groups if g.group_code == "TENANT_ADMINS"), None
                )
                if admin_group:
                    await self._add_user_to_group(
                        tenant.tenant_id, admin_user, admin_group
                    )
            
            await self.session.commit()
            
            return result
            
        except Exception as e:
            await self.session.rollback()
            raise RuntimeError(f"租户初始化失败: {str(e)}")
    
    async def _create_tenant_permissions(self, tenant_id: str) -> List[Permission]:
        """创建租户级权限"""
        tenant_permissions = [
            # 租户内用户管理
            {
                "permission_code": "tenant_user:create",
                "permission_name": "创建租户用户",
                "resource": "tenant_user",
                "action": "create",
                "description": "在租户内创建用户的权限"
            },
            {
                "permission_code": "tenant_user:manage",
                "permission_name": "管理租户用户",
                "resource": "tenant_user",
                "action": "manage",
                "description": "管理租户内用户的权限"
            },
            {
                "permission_code": "tenant_user:delete",
                "permission_name": "删除租户用户",
                "resource": "tenant_user",
                "action": "delete",
                "description": "删除租户内用户的权限"
            },
            # 租户内角色管理
            {
                "permission_code": "tenant_role:create",
                "permission_name": "创建租户角色",
                "resource": "tenant_role",
                "action": "create",
                "description": "创建租户内角色的权限"
            },
            {
                "permission_code": "tenant_role:manage",
                "permission_name": "管理租户角色",
                "resource": "tenant_role",
                "action": "manage",
                "description": "管理租户内角色的权限"
            },
            {
                "permission_code": "tenant_role:delete",
                "permission_name": "删除租户角色",
                "resource": "tenant_role",
                "action": "delete",
                "description": "删除租户内角色的权限"
            },
            # 租户内用户组管理
            {
                "permission_code": "tenant_group:create",
                "permission_name": "创建租户用户组",
                "resource": "tenant_group",
                "action": "create",
                "description": "创建租户内用户组的权限"
            },
            {
                "permission_code": "tenant_group:manage",
                "permission_name": "管理租户用户组",
                "resource": "tenant_group",
                "action": "manage",
                "description": "管理租户内用户组的权限"
            },
            # 租户配置管理
            {
                "permission_code": "tenant_config:manage",
                "permission_name": "管理租户配置",
                "resource": "tenant_config",
                "action": "manage",
                "description": "管理租户配置的权限"
            },
            # 租户审计日志
            {
                "permission_code": "tenant_audit:view",
                "permission_name": "查看租户审计日志",
                "resource": "tenant_audit",
                "action": "view",
                "description": "查看租户内审计日志的权限"
            },
            # 业务数据权限（示例）
            {
                "permission_code": "data:read",
                "permission_name": "读取数据",
                "resource": "data",
                "action": "read",
                "description": "读取业务数据的权限"
            },
            {
                "permission_code": "data:write",
                "permission_name": "写入数据",
                "resource": "data",
                "action": "write",
                "description": "写入业务数据的权限"
            },
            {
                "permission_code": "data:delete",
                "permission_name": "删除数据",
                "resource": "data",
                "action": "delete",
                "description": "删除业务数据的权限"
            }
        ]
        
        permissions = []
        for perm_data in tenant_permissions:
            permission = Permission(
                permission_id=str(uuid.uuid4()),
                tenant_id=tenant_id,
                permission_name=perm_data["permission_name"],
                permission_code=perm_data["permission_code"],
                resource=perm_data["resource"],
                action=perm_data["action"],
                description=perm_data["description"],
                level=1,
                is_inheritable=True,
                status=CommonStatus.ACTIVE
            )
            permissions.append(permission)
            self.session.add(permission)
        
        return permissions
    
    async def _create_tenant_roles(self, tenant_id: str) -> List[Role]:
        """创建租户级角色"""
        tenant_roles = [
            {
                "role_code": "TENANT_SUPER_ADMIN",
                "role_name": "租户超级管理员",
                "role_type": RoleType.TENANT_SUPER_ADMIN,
                "description": "租户内的超级管理员，拥有该租户下的所有操作权限",
                "level": 1
            },
            {
                "role_code": "TENANT_ADMIN",
                "role_name": "租户管理员",
                "role_type": RoleType.TENANT_ADMIN,
                "description": "租户管理员，负责用户和角色管理",
                "level": 2
            },
            {
                "role_code": "TENANT_USER",
                "role_name": "租户普通用户",
                "role_type": RoleType.TENANT_USER,
                "description": "租户内的普通用户",
                "level": 3
            },
            {
                "role_code": "DATA_MANAGER",
                "role_name": "数据管理员",
                "role_type": RoleType.CUSTOM,
                "description": "负责数据管理的角色",
                "level": 2
            },
            {
                "role_code": "DATA_VIEWER",
                "role_name": "数据查看员",
                "role_type": RoleType.CUSTOM,
                "description": "只能查看数据的角色",
                "level": 3
            }
        ]
        
        roles = []
        for role_data in tenant_roles:
            role = Role(
                role_id=str(uuid.uuid4()),
                tenant_id=tenant_id,
                role_name=role_data["role_name"],
                role_code=role_data["role_code"],
                role_type=role_data["role_type"],
                description=role_data["description"],
                level=role_data["level"],
                is_platform_role=False,
                is_system_role=True,  # 默认角色标记为系统角色
                status=CommonStatus.ACTIVE
            )
            roles.append(role)
            self.session.add(role)
        
        return roles
    
    async def _assign_permissions_to_roles(
        self, 
        tenant_id: str,
        roles: List[Role], 
        permissions: List[Permission]
    ):
        """为角色分配权限"""
        
        # 租户超级管理员拥有所有租户权限
        tenant_super_admin = next(
            r for r in roles if r.role_type == RoleType.TENANT_SUPER_ADMIN
        )
        for permission in permissions:
            role_permission = RolePermission(
                tenant_id=tenant_id,
                role_id=tenant_super_admin.role_id,
                permission_id=permission.permission_id,
                status=CommonStatus.ACTIVE
            )
            self.session.add(role_permission)
        
        # 租户管理员拥有用户和角色管理权限
        tenant_admin = next(
            r for r in roles if r.role_type == RoleType.TENANT_ADMIN
        )
        admin_permissions = [
            "tenant_user:create", "tenant_user:manage", "tenant_user:delete",
            "tenant_role:create", "tenant_role:manage",
            "tenant_group:create", "tenant_group:manage",
            "tenant_audit:view"
        ]
        for permission in permissions:
            if permission.permission_code in admin_permissions:
                role_permission = RolePermission(
                    tenant_id=tenant_id,
                    role_id=tenant_admin.role_id,
                    permission_id=permission.permission_id,
                    status=CommonStatus.ACTIVE
                )
                self.session.add(role_permission)
        
        # 数据管理员拥有数据相关权限
        data_manager = next(
            r for r in roles if r.role_code == "DATA_MANAGER"
        )
        data_permissions = ["data:read", "data:write", "data:delete"]
        for permission in permissions:
            if permission.permission_code in data_permissions:
                role_permission = RolePermission(
                    tenant_id=tenant_id,
                    role_id=data_manager.role_id,
                    permission_id=permission.permission_id,
                    status=CommonStatus.ACTIVE
                )
                self.session.add(role_permission)
        
        # 数据查看员只有读取权限
        data_viewer = next(
            r for r in roles if r.role_code == "DATA_VIEWER"
        )
        view_permissions = ["data:read"]
        for permission in permissions:
            if permission.permission_code in view_permissions:
                role_permission = RolePermission(
                    tenant_id=tenant_id,
                    role_id=data_viewer.role_id,
                    permission_id=permission.permission_id,
                    status=CommonStatus.ACTIVE
                )
                self.session.add(role_permission)
    
    async def _create_default_groups(self, tenant_id: str) -> List[UserGroup]:
        """创建默认用户组"""
        default_groups = [
            {
                "group_code": "TENANT_ADMINS",
                "group_name": "租户管理员组",
                "group_type": UserGroupType.ROLE_GROUP,
                "description": "租户管理员用户组"
            },
            {
                "group_code": "ALL_USERS",
                "group_name": "所有用户",
                "group_type": UserGroupType.ROLE_GROUP,
                "description": "租户内所有用户的默认组"
            },
            {
                "group_code": "DATA_TEAM",
                "group_name": "数据团队",
                "group_type": UserGroupType.DEPARTMENT,
                "description": "数据相关工作的团队"
            }
        ]
        
        groups = []
        for group_data in default_groups:
            group = UserGroup(
                group_id=str(uuid.uuid4()),
                tenant_id=tenant_id,
                group_name=group_data["group_name"],
                group_code=group_data["group_code"],
                group_type=group_data["group_type"],
                description=group_data["description"],
                level=1,
                status=CommonStatus.ACTIVE
            )
            groups.append(group)
            self.session.add(group)
        
        return groups
    
    async def _create_tenant_admin_user(
        self, 
        tenant_id: str,
        username: str, 
        email: str, 
        password: str
    ) -> User:
        """创建租户管理员用户"""
        
        # 生成密码哈希
        salt = secrets.token_hex(32)
        password_hash = hashlib.pbkdf2_hmac(
            'sha256', 
            password.encode('utf-8'), 
            salt.encode('utf-8'), 
            100000
        ).hex()
        
        user = User(
            user_id=str(uuid.uuid4()),
            tenant_id=tenant_id,
            username=username,
            email=email,
            password_hash=password_hash,
            salt=salt,
            nickname="租户管理员",
            status=CommonStatus.ACTIVE,
            profile={
                "is_tenant_admin": True,
                "created_by_system": True,
                "description": "租户初始化创建的管理员"
            }
        )
        
        self.session.add(user)
        return user
    
    async def _assign_role_to_user(self, tenant_id: str, user: User, role: Role):
        """为用户分配角色"""
        user_role = UserRole(
            tenant_id=tenant_id,
            user_id=user.user_id,
            role_id=role.role_id,
            assignment_type=AssignmentType.PERMANENT,
            status=CommonStatus.ACTIVE
        )
        self.session.add(user_role)
    
    async def _add_user_to_group(self, tenant_id: str, user: User, group: UserGroup):
        """将用户添加到用户组"""
        member = UserGroupMember(
            tenant_id=tenant_id,
            user_id=user.user_id,
            group_id=group.group_id,
            assignment_type=AssignmentType.PERMANENT,
            status=CommonStatus.ACTIVE
        )
        self.session.add(member)
    
    def _generate_secure_password(self, length: int = 16) -> str:
        """生成安全密码"""
        import string
        
        # 确保密码包含各种字符类型
        chars = string.ascii_letters + string.digits + "!@#$%^&*"
        password = ''.join(secrets.choice(chars) for _ in range(length))
        
        # 确保至少包含一个大写字母、小写字母、数字和特殊字符
        if not any(c.isupper() for c in password):
            password = password[:-1] + secrets.choice(string.ascii_uppercase)
        if not any(c.islower() for c in password):
            password = password[:-1] + secrets.choice(string.ascii_lowercase)
        if not any(c.isdigit() for c in password):
            password = password[:-1] + secrets.choice(string.digits)
        if not any(c in "!@#$%^&*" for c in password):
            password = password[:-1] + secrets.choice("!@#$%^&*")
        
        return password


# 使用示例
async def bootstrap_tenant(
    session: AsyncSession, 
    tenant: Tenant,
    admin_username: str,
    admin_email: str,
    admin_password: Optional[str] = None
) -> Dict[str, Any]:
    """租户初始化入口函数"""
    bootstrap = TenantBootstrap(session)
    
    result = await bootstrap.initialize_tenant(
        tenant=tenant,
        admin_username=admin_username,
        admin_email=admin_email,
        admin_password=admin_password
    )
    
    return result

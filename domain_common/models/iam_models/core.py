"""
IAM 核心业务模型

包含租户、用户、角色、权限等核心实体模型
"""

from datetime import datetime
from typing import Optional

from sqlalchemy.ext.asyncio import AsyncSession

from domain_common.models.base_model import Base
from domain_common.models.constants import JSONType, FieldLengths, CommonStatus, PermissionStatus, RoleType
from domain_common.models.fields import Fields
from domain_common.models.mixins import AuditManager, StatusManager, TimestampMixin, AuditMixin, SoftDeleteMixin
from sqlalchemy import (Boolean, Index, Integer, String, Text,
                        UniqueConstraint, text, select, func, and_)
from sqlalchemy.orm import Mapped, mapped_column


class Tenant(Base, TimestampMixin, AuditMixin):
    """租户实体模型

    租户是多租户架构的根基，管理组织级别的配置和限制
    """

    __tablename__ = "tenants"

    tenant_id: Mapped[str] = Fields.uuid_primary_key(doc="租户ID")
    tenant_name: Mapped[str] = Fields.name(doc="租户名称")
    tenant_code: Mapped[str] = Fields.code(doc="租户编码")
    status: Mapped[str] = Fields.status()
    description: Mapped[Optional[str]] = mapped_column(
        Text, nullable=True, doc="租户描述"
    )
    settings: Mapped[Optional[JSONType]] = Fields.json_field(
        default={}, doc="租户配置信息"
    )
    max_users: Mapped[int] = mapped_column(Integer, default=1000, doc="最大用户数")

    __table_args__ = (
        Index("idx_tenants_status", "status"),
        Index("idx_tenants_created_at", "created_at"),
    )

    @property
    def status_manager(self) -> StatusManager:
        """获取状态管理器"""
        return StatusManager(self)

    @property
    def audit_manager(self) -> AuditManager:
        """获取审计管理器"""
        return AuditManager(self)

    def __repr__(self) -> str:
        return f"<Tenant(tenant_id={self.tenant_id}, tenant_code={self.tenant_code}, status={self.status})>"

    @classmethod
    async def get_tenant_by_id(
            cls, session: AsyncSession, tenant_id: str
    ) -> Optional["Tenant"]:
        """根据租户ID获取租户实例

        :param session: SQLAlchemy异步会话
        :param tenant_id: 租户ID
        :return: Tenant 实例或 None
        """
        stmt = select(cls).where(cls.tenant_id == tenant_id)
        result = await session.execute(stmt)
        return result.scalar_one_or_none()


class User(Base, TimestampMixin, AuditMixin):
    """用户实体模型

    系统用户，支持多种登录方式和完整的安全特性
    """

    __tablename__ = "users"

    # 重写主键为 user_id
    user_id: Mapped[str] = Fields.uuid_primary_key(doc="用户ID")
    # 多租户字段
    tenant_id: Mapped[str] = Fields.tenant()

    # 基本信息字段
    username: Mapped[str] = Fields.code(max_length=100, doc="用户名")
    email: Mapped[Optional[str]] = Fields.email(required=False, unique=False)
    phone: Mapped[Optional[str]] = Fields.phone()
    nickname: Mapped[Optional[str]] = Fields.name(max_length=100, required=False, doc="用户昵称")

    # 安全字段
    password_hash: Mapped[str] = mapped_column(
        String(255), nullable=False, doc="密码哈希值"
    )
    salt: Mapped[str] = mapped_column(String(64), nullable=True, doc="密码盐值")
    # 状态字段
    status: Mapped[str] = Fields.status()

    # JSON 字段存储灵活数据
    profile: Mapped[Optional[JSONType]] = Fields.json_field(
        default={}, doc="用户资料信息"
    )
    preferences: Mapped[Optional[JSONType]] = Fields.json_field(
        default={}, doc="用户偏好设置"
    )
    security_settings: Mapped[Optional[JSONType]] = Fields.json_field(
        default={}, doc="安全设置"
    )
    mfa_enabled:Mapped[bool] = mapped_column(
        Boolean, default=False, doc="是否启用mfa功能"
    )

    # 登录安全相关字段
    password_changed_at: Mapped[datetime] = Fields.created_at()
    last_login: Mapped[Optional[datetime]] = mapped_column(
        nullable=True, doc="最后登录时间"
    )

    __table_args__ = (
        UniqueConstraint("tenant_id", "username", name="uq_users_tenant_username"),
        UniqueConstraint("tenant_id", "email", name="uq_users_tenant_email"),
        Index("idx_users_tenant_status", "tenant_id", "status"),
        Index("idx_users_email", "email"),
        Index("idx_users_phone", "phone"),
    )

    @property
    def status_manager(self) -> StatusManager:
        """获取状态管理器"""
        return StatusManager(self)

    @property
    def audit_manager(self) -> AuditManager:
        """获取审计管理器"""
        return AuditManager(self)

    def __repr__(self) -> str:
        return f"<User(user_id={self.user_id}, username={self.username}, tenant_id={self.tenant_id})>"

    @classmethod
    async def get_user_by_id(
            cls, session: AsyncSession, user_id: str
    ) -> Optional["User"]:
        """根据用户ID获取用户实例

        :param session: SQLAlchemy异步会话
        :param user_id: 用户ID
        :return: Tenant 实例或 None
        """
        stmt = select(cls).where(cls.user_id == user_id)
        result = await session.execute(stmt)
        return result.scalar_one_or_none()


class Role(Base, TimestampMixin, AuditMixin):
    """角色实体模型

    支持层级结构的角色管理，用于 RBAC 权限控制
    支持平台级角色和租户级角色的区分
    """

    __tablename__ = "roles"

    # 重写主键为 role_id
    role_id: Mapped[str] = Fields.uuid_primary_key(doc="角色ID")
    # 多租户字段（平台级角色可为空）
    tenant_id: Mapped[Optional[str]] = Fields.tenant(required=False)

    # 基本信息字段
    role_name: Mapped[str] = Fields.name(doc="角色名称")
    role_code: Mapped[str] = Fields.code(doc="角色编码")
    description: Mapped[Optional[str]] = mapped_column(
        Text, nullable=True, doc="角色描述"
    )

    # 角色类型字段
    role_type: Mapped[str] = mapped_column(
        String(50),
        nullable=False,
        default=RoleType.CUSTOM,
        doc="角色类型：platform_super_admin, tenant_super_admin等"
    )
    is_platform_role: Mapped[bool] = mapped_column(
        Boolean, default=False, doc="是否为平台级角色"
    )
    is_system_role: Mapped[bool] = mapped_column(
        Boolean, default=False, doc="是否为系统内置角色（不可删除）"
    )

    # 层级和限制字段
    level: Mapped[int] = mapped_column(Integer, default=1, doc="角色层级，建议1-3层")
    parent_role_id: Mapped[Optional[str]] = mapped_column(
        String(64), nullable=True, doc="父角色ID"
    )
    max_users: Mapped[int] = mapped_column(
        Integer, default=0, doc="最大用户数，0表示无限制"
    )

    # 状态字段
    status: Mapped[str] = Fields.status()

    # 元数据字段
    meta_data: Mapped[Optional[JSONType]] = Fields.json_field(
        default={}, doc="角色元数据"
    )

    __table_args__ = (
        # 租户级角色的唯一约束（平台角色tenant_id为NULL，不受此约束）
        Index("idx_roles_tenant_level", "tenant_id", "level"),
        Index("idx_roles_parent", "parent_role_id"),
        Index("idx_roles_tenant_status", "tenant_id", "status"),
        Index("idx_roles_type", "role_type"),
        Index("idx_roles_platform", "is_platform_role"),
        Index("idx_roles_system", "is_system_role"),
    )

    @property
    def status_manager(self) -> StatusManager:
        """获取状态管理器"""
        return StatusManager(self)

    @property
    def audit_manager(self) -> AuditManager:
        """获取审计管理器"""
        return AuditManager(self)

    def __repr__(self) -> str:
        return f"<Role(role_id={self.role_id}, role_code={self.role_code}, tenant_id={self.tenant_id})>"


class Permission(Base, TimestampMixin, AuditMixin):
    """权限实体模型

    支持层级结构的权限管理，用于细粒度的访问控制
    """

    __tablename__ = "permissions"

    permission_id: Mapped[str] = Fields.uuid_primary_key(doc="权限ID")
    tenant_id: Mapped[str] = Fields.tenant()

    # 基本信息字段
    permission_name: Mapped[str] = Fields.name(doc="权限名称")
    permission_code: Mapped[str] = Fields.code(doc="权限编码")
    resource: Mapped[str] = Fields.code(max_length=100, doc="资源类型")
    action: Mapped[str] = Fields.code(max_length=100, doc="操作类型")
    description: Mapped[Optional[str]] = mapped_column(
        Text, nullable=True, doc="权限描述"
    )

    # 层级字段
    level: Mapped[int] = mapped_column(Integer, default=1, doc="权限层级，建议1-3层")
    parent_permission_id: Mapped[Optional[str]] = mapped_column(
        String(64), nullable=True, doc="父权限ID"
    )
    is_inheritable: Mapped[bool] = mapped_column(
        Boolean, default=True, doc="是否可继承"
    )
    # 状态字段
    status: Mapped[str] = Fields.status(default=PermissionStatus.PENDING)

    # 元数据字段
    meta_data: Mapped[Optional[JSONType]] = Fields.json_field(
        default={}, doc="权限元数据（菜单URL、图标等）"
    )

    __table_args__ = (
        UniqueConstraint(
            "tenant_id", "permission_code", name="uq_permissions_tenant_code"
        ),
        Index("idx_permissions_resource_action", "resource", "action"),
        Index("idx_permissions_tenant_level", "tenant_id", "level"),
        Index("idx_permissions_parent", "parent_permission_id"),
        Index("idx_permissions_tenant_status", "tenant_id", "status"),
    )

    @property
    def status_manager(self) -> StatusManager:
        """获取状态管理器"""
        return StatusManager(self)

    @property
    def audit_manager(self) -> AuditManager:
        """获取审计管理器"""
        return AuditManager(self)

    def __repr__(self) -> str:
        return f"<Permission(permission_id={self.permission_id}, permission_code={self.permission_code}, tenant_id={self.tenant_id})>"

    @classmethod
    async def get_permission_by_id(
            cls, session: AsyncSession, permission_id: str
    ) -> Optional["Permission"]:
        """根据权限ID获取权限实例

        :param session: SQLAlchemy异步会话
        :param permission_id: 权限ID
        :return: Permission 实例或 None
        """
        stmt = select(cls).where(cls.permission_id == permission_id)
        result = await session.execute(stmt)
        return result.scalar_one_or_none()

    @classmethod
    async def get_permission_children_count(cls, session: AsyncSession, permission_id: str) -> int:
        """获取权限子权限数量"""
        stmt = select(func.count(cls.permission_id)).where(
            and_(
                cls.parent_permission_id == permission_id,
                cls.status == CommonStatus.ACTIVE
            )
        )
        result = await session.execute(stmt)
        return result.scalar() or 0
        
    @classmethod
    async def validate_permission_code_uniqueness(cls,session: AsyncSession, tenant_id: str, permission_code: str):
        """验证权限代码唯一性"""
        stmt = select(cls).where(
            and_(
                cls.tenant_id == tenant_id,
                cls.permission_code == permission_code,
                cls.status != CommonStatus.DELETED
            )
        )
        result = await session.execute(stmt)
        return result.scalar_one_or_none()


class UserGroup(Base, TimestampMixin, AuditMixin):
    """用户组实体模型

    支持组织结构映射和权限复用的用户组管理
    用于实现部门、小组、项目组等自然组织结构的权限管理
    """

    __tablename__ = "user_groups"

    # 重写主键为 group_id
    group_id: Mapped[str] = Fields.uuid_primary_key(doc="用户组ID")
    # 多租户字段
    tenant_id: Mapped[str] = Fields.tenant()

    # 基本信息字段
    group_name: Mapped[str] = Fields.name(doc="用户组名称")
    group_code: Mapped[str] = Fields.code(doc="用户组编码")
    description: Mapped[Optional[str]] = mapped_column(
        Text, nullable=True, doc="用户组描述"
    )

    # 用户组类型字段
    group_type: Mapped[str] = mapped_column(
        String(50),
        nullable=False,
        doc="用户组类型：department, team, project, role_group等"
    )

    # 层级字段
    level: Mapped[int] = mapped_column(Integer, default=1, doc="用户组层级")
    parent_group_id: Mapped[Optional[str]] = mapped_column(
        String(64), nullable=True, doc="父用户组ID"
    )

    # 限制字段
    max_members: Mapped[int] = mapped_column(
        Integer, default=0, doc="最大成员数，0表示无限制"
    )

    # 状态字段
    status: Mapped[str] = Fields.status()

    # 元数据字段
    meta_data: Mapped[Optional[JSONType]] = Fields.json_field(
        default={}, doc="用户组元数据"
    )

    __table_args__ = (
        UniqueConstraint("tenant_id", "group_code", name="uq_user_groups_tenant_code"),
        Index("idx_user_groups_tenant_type", "tenant_id", "group_type"),
        Index("idx_user_groups_parent", "parent_group_id"),
        Index("idx_user_groups_tenant_status", "tenant_id", "status"),
        Index("idx_user_groups_level", "level"),
    )

    @property
    def status_manager(self) -> StatusManager:
        """获取状态管理器"""
        return StatusManager(self)

    @property
    def audit_manager(self) -> AuditManager:
        """获取审计管理器"""
        return AuditManager(self)

    def __repr__(self) -> str:
        return f"<UserGroup(group_id={self.group_id}, group_code={self.group_code}, tenant_id={self.tenant_id})>"

    @classmethod
    async def get_group_by_id(
            cls, session: AsyncSession, group_id: str
    ) -> Optional["UserGroup"]:
        """根据用户组ID获取用户组实例

        :param session: SQLAlchemy异步会话
        :param group_id: 用户组ID
        :return: UserGroup 实例或 None
        """
        stmt = select(cls).where(cls.group_id == group_id)
        result = await session.execute(stmt)
        return result.scalar_one_or_none()

    @classmethod
    async def get_group_children_count(cls, session: AsyncSession, group_id: str) -> int:
        """获取用户组子组数量"""
        stmt = select(func.count(cls.group_id)).where(
            and_(
                cls.parent_group_id == group_id,
                cls.status == CommonStatus.ACTIVE
            )
        )
        result = await session.execute(stmt)
        return result.scalar() or 0

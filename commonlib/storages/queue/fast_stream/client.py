import time
from datetime import timed<PERSON><PERSON>
from typing import Optional

from faststream.redis import RedisBroker

from commonlib.configs.storages.queue.faststream_config import FastStreamConfig
from commonlib.core.logging.tsif_logging import app_logger
from commonlib.storages.base import BaseConnector, ConnectorParams


class FastStreamConnector(BaseConnector[RedisBroker, FastStreamConfig]):
    """FastStream Redis 消息队列连接器"""

    _config_class = FastStreamConfig

    def __init__(self, initial_params: Optional[ConnectorParams] = None) -> None:
        super().__init__(
            initial_params or ConnectorParams(heartbeat_interval=timedelta(seconds=30))
        )
        self._connection: Optional[RedisBroker] = None

    @property
    def name(self) -> str:
        return "FastStream.Redis"

    @property
    def broker(self) -> RedisBroker:
        if not self._connection:
            raise RuntimeError(f"{self.name} broker not initialized.")
        return self._connection

    async def _connect(self) -> bool:
        """初始化 RedisBroker"""
        if not self.config:
            raise RuntimeError(f"{self.name} Config not loaded")

        try:
            await self._close()

            self._connection = RedisBroker(self.config.FAST_STREAM_BROKER_URL)
            await self._connection.start()
            app_logger.info(f"{self.name} Broker initialized")
            return True
        except Exception as e:
            app_logger.error(f"{self.name} init failed: {e}", exception=True)
            return False

    async def _close(self) -> None:
        """关闭 RedisBroker"""
        if self._connection:
            try:
                await self._connection.stop()
                app_logger.info(f"{self.name} Broker closed.")
            except Exception as e:
                app_logger.warning(f"{self.name} close failed: {e}", exception=True)

        self._connection = None

    async def _perform_heartbeat_check(self) -> bool:
        """Redis 心跳检查（模拟）"""
        try:
            if not self._connection:
                return False
            # RedisBroker 没有显式连接，需要通过发布/订阅测试连通性
            await self._connection.publish({"ping": time.time()}, "health-check")
            return True
        except Exception as e:
            app_logger.error(f"{self.name} heartbeat failed: {e}", exception=True)
            return False
